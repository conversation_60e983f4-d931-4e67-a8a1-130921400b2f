'use client';

import { useEffect, useState } from 'react';

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      title: "Achieve the extraordinary",
      subtitle: "Alpha-devs delivers cutting-edge software development, AI solutions and digital transformation services – all in one platform.",
      buttonText: "Explore Our Services"
    },
    {
      title: "Transform your business with AI",
      subtitle: "Leverage our advanced AI solutions including Alpha-analytics and AI Voice Cloner to revolutionize your operations.",
      buttonText: "Discover AI Solutions"
    },
    {
      title: "Build the future today",
      subtitle: "From web development to mobile apps, we create innovative solutions that drive your business forward.",
      buttonText: "Start Your Project"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [slides.length]);

  return (
    <section className="relative gradient-primary min-h-screen flex items-center overflow-hidden">
      {/* Floating Icons */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="floating-icon absolute top-20 left-10 w-16 h-16 glass rounded-2xl neon-blue animate-float">
          <div className="w-full h-full flex items-center justify-center text-blue-400 text-2xl">⚡</div>
        </div>
        <div className="floating-icon absolute top-32 right-20 w-20 h-20 glass rounded-2xl neon-blue animate-float-delayed">
          <div className="w-full h-full flex items-center justify-center text-blue-300 text-2xl">🚀</div>
        </div>
        <div className="floating-icon absolute bottom-32 left-20 w-18 h-18 glass rounded-2xl neon-blue animate-float">
          <div className="w-full h-full flex items-center justify-center text-blue-400 text-2xl">💡</div>
        </div>
        <div className="floating-icon absolute bottom-20 right-32 w-14 h-14 glass rounded-2xl neon-blue animate-float-delayed">
          <div className="w-full h-full flex items-center justify-center text-blue-300 text-xl">🎯</div>
        </div>
        <div className="floating-icon absolute top-1/2 right-10 w-16 h-16 glass rounded-2xl neon-blue animate-float">
          <div className="w-full h-full flex items-center justify-center text-blue-400 text-2xl">🔥</div>
        </div>
        <div className="floating-icon absolute top-1/3 left-1/3 w-12 h-12 glass rounded-2xl neon-blue animate-float-delayed">
          <div className="w-full h-full flex items-center justify-center text-blue-300 text-xl">⭐</div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-10">
            <div className="space-y-6">
              <h1 className="text-6xl lg:text-7xl font-bold text-white leading-tight">
                {slides[currentSlide].title}
              </h1>
              <p className="text-xl text-gray-300 leading-relaxed max-w-2xl">
                {slides[currentSlide].subtitle}
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-6">
              <button className="btn-primary text-lg px-10 py-4">
                {slides[currentSlide].buttonText}
              </button>
              <button className="btn-secondary text-lg px-10 py-4">
                Learn More
              </button>
            </div>

            {/* Slide Indicators */}
            <div className="flex space-x-3">
              {slides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlide(index)}
                  className={`w-4 h-4 rounded-full transition-all duration-300 ${
                    index === currentSlide ? 'bg-blue-400 neon-blue' : 'bg-gray-600 hover:bg-gray-500'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Visual Elements */}
          <div className="relative">
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-6">
                <div className="card-sleek p-8 transform rotate-3 hover:rotate-0 transition-all duration-500">
                  <div className="w-14 h-14 gradient-accent rounded-xl mb-6 neon-blue"></div>
                  <h3 className="font-bold text-white text-lg mb-2">Web Development</h3>
                  <p className="text-gray-400 text-sm">Modern, responsive websites</p>
                </div>
                <div className="card-sleek p-8 transform -rotate-2 hover:rotate-0 transition-all duration-500">
                  <div className="w-14 h-14 gradient-accent rounded-xl mb-6 neon-blue"></div>
                  <h3 className="font-bold text-white text-lg mb-2">AI Solutions</h3>
                  <p className="text-gray-400 text-sm">Cutting-edge AI technology</p>
                </div>
              </div>
              <div className="space-y-6 mt-12">
                <div className="card-sleek p-8 transform rotate-2 hover:rotate-0 transition-all duration-500">
                  <div className="w-14 h-14 gradient-accent rounded-xl mb-6 neon-blue"></div>
                  <h3 className="font-bold text-white text-lg mb-2">Mobile Apps</h3>
                  <p className="text-gray-400 text-sm">iOS & Android development</p>
                </div>
                <div className="card-sleek p-8 transform -rotate-1 hover:rotate-0 transition-all duration-500">
                  <div className="w-14 h-14 gradient-accent rounded-xl mb-6 neon-blue"></div>
                  <h3 className="font-bold text-white text-lg mb-2">Consulting</h3>
                  <p className="text-gray-400 text-sm">Expert technical guidance</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
