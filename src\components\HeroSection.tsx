'use client';

import { useEffect, useState } from 'react';

const HeroSection = () => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const slides = [
    {
      title: "Achieve the extraordinary",
      subtitle: "Alpha-devs delivers cutting-edge software development, AI solutions and digital transformation services – all in one platform.",
      buttonText: "Explore Our Services"
    },
    {
      title: "Transform your business with AI",
      subtitle: "Leverage our advanced AI solutions including Alpha-analytics and AI Voice Cloner to revolutionize your operations.",
      buttonText: "Discover AI Solutions"
    },
    {
      title: "Build the future today",
      subtitle: "From web development to mobile apps, we create innovative solutions that drive your business forward.",
      buttonText: "Start Your Project"
    }
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 5000);

    return () => clearInterval(timer);
  }, [slides.length]);

  return (
    <section className="relative bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 min-h-screen flex items-center overflow-hidden">
      {/* Floating Icons */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="floating-icon absolute top-20 left-10 w-16 h-16 bg-blue-500 rounded-lg opacity-80 animate-float">
          <div className="w-full h-full flex items-center justify-center text-white text-2xl">⚡</div>
        </div>
        <div className="floating-icon absolute top-32 right-20 w-20 h-20 bg-purple-500 rounded-lg opacity-70 animate-float-delayed">
          <div className="w-full h-full flex items-center justify-center text-white text-2xl">🚀</div>
        </div>
        <div className="floating-icon absolute bottom-32 left-20 w-18 h-18 bg-green-500 rounded-lg opacity-75 animate-float">
          <div className="w-full h-full flex items-center justify-center text-white text-2xl">💡</div>
        </div>
        <div className="floating-icon absolute bottom-20 right-32 w-14 h-14 bg-orange-500 rounded-lg opacity-80 animate-float-delayed">
          <div className="w-full h-full flex items-center justify-center text-white text-xl">🎯</div>
        </div>
        <div className="floating-icon absolute top-1/2 right-10 w-16 h-16 bg-red-500 rounded-lg opacity-70 animate-float">
          <div className="w-full h-full flex items-center justify-center text-white text-2xl">🔥</div>
        </div>
        <div className="floating-icon absolute top-1/3 left-1/3 w-12 h-12 bg-cyan-500 rounded-lg opacity-60 animate-float-delayed">
          <div className="w-full h-full flex items-center justify-center text-white text-xl">⭐</div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div className="space-y-4">
              <h1 className="text-5xl lg:text-6xl font-bold text-gray-900 leading-tight">
                {slides[currentSlide].title}
              </h1>
              <p className="text-xl text-gray-600 leading-relaxed max-w-lg">
                {slides[currentSlide].subtitle}
              </p>
            </div>
            
            <div className="flex flex-col sm:flex-row gap-4">
              <button className="bg-blue-600 text-white px-8 py-4 rounded-md text-lg font-semibold hover:bg-blue-700 transition-colors">
                {slides[currentSlide].buttonText}
              </button>
              <button className="border border-gray-300 text-gray-700 px-8 py-4 rounded-md text-lg font-semibold hover:bg-gray-50 transition-colors">
                Learn More
              </button>
            </div>

            {/* Slide Indicators */}
            <div className="flex space-x-2">
              {slides.map((_, index) => (
                <button
                  key={index}
                  onClick={() => setCurrentSlide(index)}
                  className={`w-3 h-3 rounded-full transition-colors ${
                    index === currentSlide ? 'bg-blue-600' : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>

          {/* Visual Elements */}
          <div className="relative">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-4">
                <div className="bg-white p-6 rounded-lg shadow-lg transform rotate-3 hover:rotate-0 transition-transform">
                  <div className="w-12 h-12 bg-blue-600 rounded-lg mb-4"></div>
                  <h3 className="font-semibold text-gray-900">Web Development</h3>
                  <p className="text-gray-600 text-sm">Modern, responsive websites</p>
                </div>
                <div className="bg-white p-6 rounded-lg shadow-lg transform -rotate-2 hover:rotate-0 transition-transform">
                  <div className="w-12 h-12 bg-purple-600 rounded-lg mb-4"></div>
                  <h3 className="font-semibold text-gray-900">AI Solutions</h3>
                  <p className="text-gray-600 text-sm">Cutting-edge AI technology</p>
                </div>
              </div>
              <div className="space-y-4 mt-8">
                <div className="bg-white p-6 rounded-lg shadow-lg transform rotate-2 hover:rotate-0 transition-transform">
                  <div className="w-12 h-12 bg-green-600 rounded-lg mb-4"></div>
                  <h3 className="font-semibold text-gray-900">Mobile Apps</h3>
                  <p className="text-gray-600 text-sm">iOS & Android development</p>
                </div>
                <div className="bg-white p-6 rounded-lg shadow-lg transform -rotate-1 hover:rotate-0 transition-transform">
                  <div className="w-12 h-12 bg-orange-600 rounded-lg mb-4"></div>
                  <h3 className="font-semibold text-gray-900">Consulting</h3>
                  <p className="text-gray-600 text-sm">Expert technical guidance</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
