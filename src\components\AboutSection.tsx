const AboutSection = () => {
  return (
    <section className="py-24 bg-black">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-center">
          {/* Content */}
          <div className="space-y-10">
            <div>
              <h2 className="text-5xl font-bold gradient-text mb-8">About Us</h2>
              <div className="space-y-6 text-gray-300 leading-relaxed text-lg">
                <p className="text-lg">
                  <strong>Since 2020</strong>
                </p>
                <p>
                  <strong>Idea of Alpha-Devs</strong>
                </p>
                <p>
                  Alpha-Devs is a leading software development company specializing in 
                  cutting-edge technology solutions. We are passionate about creating 
                  innovative software that transforms businesses and drives growth.
                </p>
                <p>
                  Our team of expert developers, designers, and consultants work 
                  collaboratively to deliver exceptional results that exceed our 
                  clients' expectations. From web development to AI solutions, 
                  we cover the full spectrum of modern software development.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-10">
              <div className="text-center">
                <div className="text-4xl font-bold gradient-text-accent mb-3">50+</div>
                <div className="text-gray-400 font-medium">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold gradient-text-accent mb-3">25+</div>
                <div className="text-gray-400 font-medium">Happy Clients</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold gradient-text-accent mb-3">4+</div>
                <div className="text-gray-400 font-medium">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-4xl font-bold gradient-text-accent mb-3">10+</div>
                <div className="text-gray-400 font-medium">Team Members</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-6">
              <button className="btn-primary px-10 py-4 text-lg">
                Learn More About Us
              </button>
              <button className="btn-secondary px-10 py-4 text-lg">
                View Our Work
              </button>
            </div>
          </div>

          {/* Visual */}
          <div className="relative">
            <div className="gradient-secondary rounded-3xl p-10 neon-blue">
              <div className="grid grid-cols-2 gap-8">
                <div className="space-y-6">
                  <div className="card-sleek p-6">
                    <div className="w-12 h-12 gradient-accent rounded-xl mb-4 neon-blue"></div>
                    <h4 className="font-bold text-white mb-2 text-lg">Innovation</h4>
                    <p className="text-gray-400">Cutting-edge solutions</p>
                  </div>
                  <div className="card-sleek p-6">
                    <div className="w-12 h-12 gradient-accent rounded-xl mb-4 neon-blue"></div>
                    <h4 className="font-bold text-white mb-2 text-lg">Quality</h4>
                    <p className="text-gray-400">Excellence in every project</p>
                  </div>
                </div>
                <div className="space-y-6 mt-12">
                  <div className="card-sleek p-6">
                    <div className="w-12 h-12 gradient-accent rounded-xl mb-4 neon-blue"></div>
                    <h4 className="font-bold text-white mb-2 text-lg">Expertise</h4>
                    <p className="text-gray-400">Deep technical knowledge</p>
                  </div>
                  <div className="card-sleek p-6">
                    <div className="w-12 h-12 gradient-accent rounded-xl mb-4 neon-blue"></div>
                    <h4 className="font-bold text-white mb-2 text-lg">Support</h4>
                    <p className="text-gray-400">24/7 customer care</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
