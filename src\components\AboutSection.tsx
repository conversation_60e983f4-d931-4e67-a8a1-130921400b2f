const AboutSection = () => {
  return (
    <section className="py-20 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">About Us</h2>
              <div className="space-y-6 text-gray-600 leading-relaxed">
                <p className="text-lg">
                  <strong>Since 2020</strong>
                </p>
                <p>
                  <strong>Idea of Alpha-Devs</strong>
                </p>
                <p>
                  Alpha-Devs is a leading software development company specializing in 
                  cutting-edge technology solutions. We are passionate about creating 
                  innovative software that transforms businesses and drives growth.
                </p>
                <p>
                  Our team of expert developers, designers, and consultants work 
                  collaboratively to deliver exceptional results that exceed our 
                  clients' expectations. From web development to AI solutions, 
                  we cover the full spectrum of modern software development.
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-8">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">50+</div>
                <div className="text-gray-600">Projects Completed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">25+</div>
                <div className="text-gray-600">Happy Clients</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">4+</div>
                <div className="text-gray-600">Years Experience</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">10+</div>
                <div className="text-gray-600">Team Members</div>
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <button className="bg-blue-600 text-white px-8 py-3 rounded-md font-semibold hover:bg-blue-700 transition-colors">
                Learn More About Us
              </button>
              <button className="border border-gray-300 text-gray-700 px-8 py-3 rounded-md font-semibold hover:bg-gray-50 transition-colors">
                View Our Work
              </button>
            </div>
          </div>

          {/* Visual */}
          <div className="relative">
            <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-8">
              <div className="grid grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-white rounded-lg p-4 shadow-sm">
                    <div className="w-8 h-8 bg-blue-600 rounded-lg mb-3"></div>
                    <h4 className="font-semibold text-gray-900 mb-1">Innovation</h4>
                    <p className="text-sm text-gray-600">Cutting-edge solutions</p>
                  </div>
                  <div className="bg-white rounded-lg p-4 shadow-sm">
                    <div className="w-8 h-8 bg-green-600 rounded-lg mb-3"></div>
                    <h4 className="font-semibold text-gray-900 mb-1">Quality</h4>
                    <p className="text-sm text-gray-600">Excellence in every project</p>
                  </div>
                </div>
                <div className="space-y-4 mt-8">
                  <div className="bg-white rounded-lg p-4 shadow-sm">
                    <div className="w-8 h-8 bg-purple-600 rounded-lg mb-3"></div>
                    <h4 className="font-semibold text-gray-900 mb-1">Expertise</h4>
                    <p className="text-sm text-gray-600">Deep technical knowledge</p>
                  </div>
                  <div className="bg-white rounded-lg p-4 shadow-sm">
                    <div className="w-8 h-8 bg-orange-600 rounded-lg mb-3"></div>
                    <h4 className="font-semibold text-gray-900 mb-1">Support</h4>
                    <p className="text-sm text-gray-600">24/7 customer care</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutSection;
