const ServicesSection = () => {
  const services = [
    {
      title: "WEB DEVELOPMENT",
      subtitle: "COMPLETE WEB SOLUTIONS",
      description: "Modern, responsive websites built with cutting-edge technologies",
      icon: "🌐",
      color: "bg-blue-600"
    },
    {
      title: "MOBILE DEVELOPMENT",
      subtitle: "iOS & ANDROID APPS",
      description: "Native and cross-platform mobile applications",
      icon: "📱",
      color: "bg-green-600"
    },
    {
      title: "SOFTWARE CONSULTANCY",
      subtitle: "TECHNICAL EXPERTISE & GUIDANCE",
      description: "Expert consulting for your software development needs",
      icon: "💼",
      color: "bg-purple-600"
    },
    {
      title: "SHOPIFY & WORDPRESS",
      subtitle: "E-COMMERCE & CMS SOLUTIONS",
      description: "Custom e-commerce and content management solutions",
      icon: "🛒",
      color: "bg-orange-600"
    },
    {
      title: "WEB DESIGN AND UI/UX",
      subtitle: "BEAUTIFUL USER EXPERIENCES",
      description: "Stunning designs that convert visitors into customers",
      icon: "🎨",
      color: "bg-pink-600"
    },
    {
      title: "AI ML & DEEP LEARNING",
      subtitle: "ARTIFICIAL INTELLIGENCE SOLUTIONS",
      description: "Advanced AI and machine learning implementations",
      icon: "🤖",
      color: "bg-indigo-600"
    }
  ];

  return (
    <section className="py-20 bg-gray-900 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold mb-4">Our Services</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            We provide comprehensive software development and digital transformation services 
            to help your business thrive in the digital age.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {services.map((service, index) => (
            <div
              key={index}
              className="group bg-gray-800 rounded-lg p-8 hover:bg-gray-700 transition-all duration-300 border border-gray-700 hover:border-gray-600"
            >
              <div className="flex items-start justify-between mb-6">
                <div className={`w-12 h-12 ${service.color} rounded-lg flex items-center justify-center text-2xl`}>
                  {service.icon}
                </div>
                <button className="text-gray-400 hover:text-white transition-colors">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
              
              <div className="space-y-4">
                <div>
                  <h3 className="text-xl font-bold text-white mb-2">{service.title}</h3>
                  <p className="text-sm text-gray-400 font-medium">{service.subtitle}</p>
                </div>
                
                <p className="text-gray-300 leading-relaxed">{service.description}</p>
                
                <button className="text-blue-400 hover:text-blue-300 font-medium flex items-center space-x-2 group-hover:translate-x-1 transition-transform">
                  <span>Learn More</span>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </button>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-16">
          <button className="bg-blue-600 text-white px-8 py-4 rounded-md text-lg font-semibold hover:bg-blue-700 transition-colors">
            View All Services
          </button>
        </div>
      </div>
    </section>
  );
};

export default ServicesSection;
