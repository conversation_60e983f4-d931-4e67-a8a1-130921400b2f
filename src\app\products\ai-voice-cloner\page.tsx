import Header from '@/components/Header';
import Footer from '@/components/Footer';

export default function AIVoiceCloner() {
  return (
    <div className="min-h-screen">
      <Header />
      
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-50 via-pink-50 to-indigo-50 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <h1 className="text-5xl font-bold text-gray-900 mb-6">AI Voice Cloner</h1>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto mb-8">
              Revolutionary AI technology that creates natural-sounding voice clones from just a few minutes of audio. 
              Perfect for content creation, accessibility, and personalized experiences.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-purple-600 text-white px-8 py-4 rounded-md text-lg font-semibold hover:bg-purple-700 transition-colors">
                Try Voice Cloning
              </button>
              <button className="border border-gray-300 text-gray-700 px-8 py-4 rounded-md text-lg font-semibold hover:bg-gray-50 transition-colors">
                Listen to Samples
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Advanced Voice Technology</h2>
            <p className="text-xl text-gray-600">State-of-the-art AI for realistic voice synthesis</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <div className="bg-gray-50 rounded-lg p-8">
              <div className="w-12 h-12 bg-purple-600 rounded-lg mb-6 flex items-center justify-center">
                <span className="text-white text-2xl">🎤</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Quick Training</h3>
              <p className="text-gray-600">Create high-quality voice clones with just 5-10 minutes of audio samples.</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-8">
              <div className="w-12 h-12 bg-pink-600 rounded-lg mb-6 flex items-center justify-center">
                <span className="text-white text-2xl">🔊</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Natural Sound</h3>
              <p className="text-gray-600">Generate speech that's virtually indistinguishable from the original voice.</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-8">
              <div className="w-12 h-12 bg-indigo-600 rounded-lg mb-6 flex items-center justify-center">
                <span className="text-white text-2xl">🌍</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Multi-Language</h3>
              <p className="text-gray-600">Support for 50+ languages with accurate pronunciation and intonation.</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-8">
              <div className="w-12 h-12 bg-blue-600 rounded-lg mb-6 flex items-center justify-center">
                <span className="text-white text-2xl">⚡</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Real-time Generation</h3>
              <p className="text-gray-600">Generate speech in real-time for live applications and streaming.</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-8">
              <div className="w-12 h-12 bg-green-600 rounded-lg mb-6 flex items-center justify-center">
                <span className="text-white text-2xl">🎛️</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Voice Control</h3>
              <p className="text-gray-600">Fine-tune emotion, pace, and style to match your specific needs.</p>
            </div>

            <div className="bg-gray-50 rounded-lg p-8">
              <div className="w-12 h-12 bg-orange-600 rounded-lg mb-6 flex items-center justify-center">
                <span className="text-white text-2xl">🔒</span>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-4">Privacy First</h3>
              <p className="text-gray-600">Your voice data is encrypted and never shared with third parties.</p>
            </div>
          </div>
        </div>
      </section>

      {/* Use Cases Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">Use Cases</h2>
            <p className="text-xl text-gray-600">Endless possibilities for voice cloning technology</p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white rounded-lg p-8 shadow-sm">
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Content Creation</h3>
              <p className="text-gray-600 mb-6">
                Create podcasts, audiobooks, and video narrations without recording sessions. 
                Perfect for content creators who want consistent voice quality.
              </p>
              <ul className="space-y-2 text-gray-600">
                <li>• Podcast production</li>
                <li>• YouTube narration</li>
                <li>• Audiobook creation</li>
                <li>• E-learning content</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg p-8 shadow-sm">
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Business Applications</h3>
              <p className="text-gray-600 mb-6">
                Enhance customer experience with personalized voice interactions and 
                automated customer service that sounds human.
              </p>
              <ul className="space-y-2 text-gray-600">
                <li>• Customer service bots</li>
                <li>• Interactive voice response</li>
                <li>• Personalized marketing</li>
                <li>• Training materials</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg p-8 shadow-sm">
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Accessibility</h3>
              <p className="text-gray-600 mb-6">
                Help people with speech disabilities communicate using their own voice, 
                or preserve voices for those facing voice loss.
              </p>
              <ul className="space-y-2 text-gray-600">
                <li>• Voice preservation</li>
                <li>• Speech assistance</li>
                <li>• Communication aids</li>
                <li>• Therapeutic applications</li>
              </ul>
            </div>

            <div className="bg-white rounded-lg p-8 shadow-sm">
              <h3 className="text-2xl font-semibold text-gray-900 mb-4">Entertainment</h3>
              <p className="text-gray-600 mb-6">
                Create unique entertainment experiences with voice acting, 
                character voices, and interactive storytelling.
              </p>
              <ul className="space-y-2 text-gray-600">
                <li>• Voice acting</li>
                <li>• Game characters</li>
                <li>• Interactive stories</li>
                <li>• Virtual assistants</li>
              </ul>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-pink-600 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-bold mb-6">Ready to Clone Your Voice?</h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto">
            Join thousands of creators, businesses, and individuals who are already using 
            our AI Voice Cloner to transform their audio content.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="bg-white text-purple-600 px-8 py-4 rounded-md text-lg font-semibold hover:bg-gray-100 transition-colors">
              Start Free Trial
            </button>
            <button className="border border-white text-white px-8 py-4 rounded-md text-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors">
              Contact Sales
            </button>
          </div>
        </div>
      </section>

      <Footer />
    </div>
  );
}
